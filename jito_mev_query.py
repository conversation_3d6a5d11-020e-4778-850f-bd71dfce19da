from validator_analyzer import ValidatorAnalyzer

# Configuration constants
TOP_N_VALIDATORS = 100
NUM_EPOCHS_TO_QUERY = 5


def main():
    print(f"Fetching top {TOP_N_VALIDATORS} Jito validators...")
    top_validators = get_top_jito_validators(limit=TOP_N_VALIDATORS)

    if not top_validators:
        print("Failed to fetch validator data. Exiting.")
        return

    results = []
    print("Processing validators and fetching MEV rewards...")

    for i, validator in enumerate(top_validators):
        vote_account = validator.get('vote_account')
        identity = validator.get('identity')

        if vote_account:
            # Query rewards and handle rate limiting
            mev_rewards_sol = get_mev_rewards(vote_account, num_epochs=NUM_EPOCHS_TO_QUERY)

            results.append({
                "rank": i + 1,
                "identity": identity,
                "vote_account": vote_account,
                "total_mev_rewards_sol_last_epochs": mev_rewards_sol,
                "commission_bps": validator.get('mev_commission_bps', 'N/A')
            })

            # Print progress
            print(f"  [{i + 1}/{TOP_N_VALIDATORS}] Fetched rewards for {identity} ({vote_account})")


    # Sort results by MEV rewards
    sorted_results = sorted(results, key=lambda x: x['total_mev_rewards_sol_last_epochs'], reverse=True)

    # Print the final report
    print("\n--- MEV Rewards Report (Top 100 Jito Validators) ---")
    print(f"Total MEV rewards shown for the last {NUM_EPOCHS_TO_QUERY} epochs.")
    print("-" * 70)
    for res in sorted_results:
        print(
            f"Rank {res['rank']}: {res['identity']} ({res['vote_account']})\n"
            f"  -> MEV Rewards: {res['total_mev_rewards_sol_last_epochs']:.6f} SOL\n"
            f"  -> MEV Commission: {res['commission_bps'] / 100 if res['commission_bps'] != 'N/A' else 'N/A'}%\n"
        )
    print("-" * 70)

    # Save the report to a JSON file
    with open("jito_mev_report.json", "w") as f:
        json.dump(sorted_results, f, indent=4)

    print("\nReport saved to jito_mev_report.json")


if __name__ == "__main__":
    main()

