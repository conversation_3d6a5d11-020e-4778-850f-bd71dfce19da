import os

import requests
import json
import time

# Jito API endpoints
JITO_VALIDATORS_API = "https://kobe.mainnet.jito.network/api/v1/validators"
JITO_REWARDS_API = "https://kobe.mainnet.jito.network/api/v1/validators/"

# Number of top validators to query
TOP_N_VALIDATORS = 100

# Number of recent epochs to retrieve rewards for
NUM_EPOCHS_TO_QUERY = 5


def get_top_jito_validators(limit=100):
    """
    Fetches the list of Jito validators, sorts by stake, and returns the top N.
    """
    try:
        response = requests.get(JITO_VALIDATORS_API)
        response.raise_for_status()  # Raise an exception for bad status codes
        validators_data = response.json()

        # Sort validators by stake amount in descending order
        sorted_validators = sorted(validators_data['validators'], key=lambda x: x.get('active_stake', 0), reverse=True)

        # Get the top N validators
        top_n_validators = sorted_validators[:limit]

        return top_n_validators
    except requests.RequestException as e:
        print(f"Error fetching validators: {e}")
        return []


def get_mev_rewards(vote_account, num_epochs=5):
    """
    Fetches MEV rewards for a specific validator's vote account.
    """
    try:
        # check if json file exists for vote_account, if so, load from file instead of querying API
        if f"rewards_{vote_account}.json" in os.listdir("jsons/"):
            with open(f"jsons/rewards_{vote_account}.json", "r") as f:
                rewards_data = json.load(f)
        else:
            url = f"{JITO_REWARDS_API}{vote_account}"
            response = requests.get(url)
            time.sleep(0.5)  # Add a small delay to avoid hitting API rate limits
            response.raise_for_status()
            rewards_data = response.json()

            #save response json to file
            with open(f"jsons/rewards_{vote_account}.json", "w") as f:
                json.dump(rewards_data, f, indent=4)

        # Take rewards for the specified number of recent epochs
        recent_rewards = rewards_data[:num_epochs]

        total_mev_lamports = sum(item['mev_rewards'] for item in recent_rewards)

        # Convert lamports to SOL (1 SOL = 1 billion lamports)
        total_mev_sol = total_mev_lamports / 1_000_000_000

        return total_mev_sol
    except requests.RequestException as e:
        print(f"Error fetching MEV rewards for {vote_account}: {e}")
        return 0.0


def main():
    print(f"Fetching top {TOP_N_VALIDATORS} Jito validators...")
    top_validators = get_top_jito_validators(limit=TOP_N_VALIDATORS)

    if not top_validators:
        print("Failed to fetch validator data. Exiting.")
        return

    results = []
    print("Processing validators and fetching MEV rewards...")

    for i, validator in enumerate(top_validators):
        vote_account = validator.get('vote_account')
        identity = validator.get('identity')

        if vote_account:
            # Query rewards and handle rate limiting
            mev_rewards_sol = get_mev_rewards(vote_account, num_epochs=NUM_EPOCHS_TO_QUERY)

            results.append({
                "rank": i + 1,
                "identity": identity,
                "vote_account": vote_account,
                "total_mev_rewards_sol_last_epochs": mev_rewards_sol,
                "commission_bps": validator.get('mev_commission_bps', 'N/A')
            })

            # Print progress
            print(f"  [{i + 1}/{TOP_N_VALIDATORS}] Fetched rewards for {identity} ({vote_account})")


    # Sort results by MEV rewards
    sorted_results = sorted(results, key=lambda x: x['total_mev_rewards_sol_last_epochs'], reverse=True)

    # Print the final report
    print("\n--- MEV Rewards Report (Top 100 Jito Validators) ---")
    print(f"Total MEV rewards shown for the last {NUM_EPOCHS_TO_QUERY} epochs.")
    print("-" * 70)
    for res in sorted_results:
        print(
            f"Rank {res['rank']}: {res['identity']} ({res['vote_account']})\n"
            f"  -> MEV Rewards: {res['total_mev_rewards_sol_last_epochs']:.6f} SOL\n"
            f"  -> MEV Commission: {res['commission_bps'] / 100 if res['commission_bps'] != 'N/A' else 'N/A'}%\n"
        )
    print("-" * 70)

    # Save the report to a JSON file
    with open("jito_mev_report.json", "w") as f:
        json.dump(sorted_results, f, indent=4)

    print("\nReport saved to jito_mev_report.json")


if __name__ == "__main__":
    main()

