from validator_analyzer import ValidatorAnalyzer

# Configuration constants
TOP_N_VALIDATORS = 100
NUM_EPOCHS_TO_QUERY = 5

def main():
    """Main function that orchestrates the validator analysis using the new class-based architecture."""
    # Initialize the validator analyzer
    analyzer = ValidatorAnalyzer(use_cache=True, rate_limit_delay=0.5)

    # Perform the analysis
    results = analyzer.analyze_top_validators(
        limit=TOP_N_VALIDATORS,
        num_epochs=NUM_EPOCHS_TO_QUERY
    )

    if not results:
        print("No results to process. Exiting.")
        return

    # Generate and display the report
    analyzer.generate_report(results, num_epochs=NUM_EPOCHS_TO_QUERY)

    # Save the report to JSON file with descriptive naming
    analyzer.save_report(results, limit=TOP_N_VALIDATORS, num_epochs=NUM_EPOCHS_TO_QUERY)

    # Print summary statistics
    analyzer.print_summary_stats(results)


if __name__ == "__main__":
    main()

