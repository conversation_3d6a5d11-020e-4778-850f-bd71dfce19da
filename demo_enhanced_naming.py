#!/usr/bin/env python3
"""
Demonstration script showing the enhanced naming functionality.
This script shows the before/after comparison of file naming schemes.
"""

from utils import Utils
import os


def demo_enhanced_naming():
    """Demonstrate the enhanced naming functionality."""
    print("🚀 Enhanced Naming Functionality Demo")
    print("=" * 50)
    print()
    
    # Sample validator data
    validators = [
        {"name": "Solana Foundation", "vote_account": "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m"},
        {"name": "Coinbase Validator", "vote_account": "CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB"},
        {"name": "Binance Staking", "vote_account": "HZKopZYvv8v6un2H6KUNVQCnK5zM9emKKezvqhTBSpEc"},
        {"name": "Kraken Validator", "vote_account": "KRAKEnMdmT4EfM8ykTFH6yLoCd5vNLcQvJwF66Y2dag"},
        {"name": None, "vote_account": "he1iusunGwqrNtafDtLdhsUQDFvo13z9sUa36PauBtk"},  # Unknown
    ]
    
    print("📁 Cache File Naming Comparison")
    print("-" * 30)
    print()
    
    for i, validator in enumerate(validators, 1):
        name = validator["name"]
        vote_account = validator["vote_account"]
        
        # Old naming scheme
        old_filename = f"rewards_{vote_account}.json"
        
        # New naming scheme
        new_filename = Utils.get_cache_filename(vote_account, "rewards", name)
        
        print(f"Validator {i}: {name or 'Unknown'}")
        print(f"  📄 Old: {old_filename}")
        print(f"  ✨ New: {new_filename}")
        print(f"  📏 Length: {len(old_filename)} → {len(new_filename)} chars")
        print()
    
    print("📊 Report File Naming Examples")
    print("-" * 30)
    print()
    
    # Report naming examples
    report_examples = [
        {"limit": 100, "epochs": 5, "desc": "Standard analysis"},
        {"limit": 50, "epochs": 10, "desc": "Detailed analysis (fewer validators, more epochs)"},
        {"limit": 25, "epochs": 3, "desc": "Quick analysis"},
        {"limit": None, "epochs": None, "desc": "Basic report (no parameters)"},
    ]
    
    for example in report_examples:
        old_name = "jito_mev_report.json"
        new_name = Utils.generate_report_filename(
            base_name="jito_mev_report",
            limit=example["limit"],
            num_epochs=example["epochs"]
        )
        
        print(f"📈 {example['desc']}")
        print(f"  📄 Old: {old_name}")
        print(f"  ✨ New: {new_name}")
        print()
    
    print("🛡️ Filename Sanitization Examples")
    print("-" * 30)
    print()
    
    # Sanitization examples
    problematic_names = [
        "Validator/Name<>With:Special*Chars",
        "Name with spaces and symbols!@#$%",
        "Very_Long_Validator_Name_That_Exceeds_Normal_Length_Limits_And_Should_Be_Truncated_For_Safety",
        "Normal Validator Name",
        "",
        None,
        "none",
        "UPPERCASE VALIDATOR",
    ]
    
    for name in problematic_names:
        sanitized = Utils.sanitize_filename(name)
        status = "✅ Safe" if sanitized != "unknown" else "⚠️ Fallback"
        
        print(f"  Input:  '{name}'")
        print(f"  Output: '{sanitized}' {status}")
        print()
    
    print("🔄 Backward Compatibility Demo")
    print("-" * 30)
    print()
    
    # Show how the system handles existing files
    print("The enhanced system maintains full backward compatibility:")
    print("  1. 🔍 First tries new naming: rewards_ValidatorName_VoteAccount.json")
    print("  2. 🔄 Falls back to old naming: rewards_VoteAccount.json")
    print("  3. 📦 Migrates old files to new naming when validator names become available")
    print("  4. ✅ No data loss or cache invalidation")
    print()
    
    print("📈 Benefits Summary")
    print("-" * 30)
    print()
    
    benefits = [
        "🏷️  Human-readable cache filenames",
        "📊 Descriptive report filenames with parameters",
        "🔍 Easier cache file management and debugging",
        "🛡️  Safe filename handling for all operating systems",
        "🔄 Seamless backward compatibility",
        "📦 Automatic migration of existing cache files",
        "⚡ No performance impact on existing functionality",
        "🎯 Better organization for large validator datasets"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print()
    print("=" * 50)
    print("✨ Enhanced naming makes validator analysis more organized!")
    print()
    
    # Show actual cache directory if it exists
    if os.path.exists("jsons"):
        cache_files = Utils.list_cache_files()
        old_style = [f for f in cache_files if f.count('_') == 2]
        new_style = [f for f in cache_files if f.count('_') >= 3]
        
        print(f"📁 Current Cache Directory Status:")
        print(f"  📄 Old naming scheme: {len(old_style)} files")
        print(f"  ✨ New naming scheme: {len(new_style)} files")
        print(f"  📊 Total cache files: {len(cache_files)} files")
        
        if new_style:
            print(f"  🎯 Example new files:")
            for example in new_style[:3]:
                print(f"    • {example}")


if __name__ == "__main__":
    demo_enhanced_naming()
