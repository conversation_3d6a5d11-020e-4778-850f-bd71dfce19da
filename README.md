# Solana Personal Delegator

A Python application for analyzing Solana validators and their MEV rewards through Jito APIs, with a clean separation between Jito and Solana functionality.

## Architecture

The codebase has been refactored into a modular, class-based architecture that separates concerns:

### Core Classes

#### 1. `JitoClient` (`jito_client.py`)
Handles all Jito-specific API interactions:
- Fetches validator lists from Jito APIs
- Retrieves MEV rewards data
- Implements rate limiting and caching
- Processes Jito-specific data formats

#### 2. `SolanaClient` (`solana_client.py`)
Manages direct Solana RPC calls:
- Connects to Solana RPC endpoints
- Fetches validator information from the blockchain
- Retrieves epoch and cluster data
- Handles Solana-specific data structures

#### 3. `Utils` (`utils.py`)
Provides shared utility functions:
- Lamports ↔ SOL conversions
- File caching operations
- Common data processing utilities

#### 4. `ValidatorAnalyzer` (`validator_analyzer.py`)
Main application logic and orchestration:
- Coordinates between Jito and Solana clients
- Processes and analyzes validator data
- Generates reports and statistics
- Manages the overall workflow

## Usage

### Basic Usage (Original Functionality)

Run the main analysis script:

```bash
python jito_mev_query.py
```

This will:
1. Fetch the top 100 Jito validators by stake
2. Retrieve MEV rewards for the last 5 epochs
3. Generate a detailed report
4. Save results to `jito_mev_report.json`
5. Display summary statistics

### Advanced Usage (Individual Classes)

You can now use the classes independently for custom analysis:

```python
from jito_client import JitoClient
from solana_client import SolanaClient
from utils import Utils

# Use Jito client independently
jito = JitoClient(rate_limit_delay=0.5, use_cache=True)
validators = jito.get_validators(limit=10)
rewards = jito.get_mev_rewards("vote_account_address", num_epochs=3)

# Use Solana client independently
solana = SolanaClient()
epoch_info = solana.get_epoch_info()
vote_accounts = solana.get_vote_accounts()

# Use utilities
sol_amount = Utils.lamports_to_sol(1_500_000_000)  # Convert 1.5 SOL
Utils.save_cached_data("my_data.json", {"key": "value"})
```

See `example_usage.py` for more detailed examples.

### Custom Analysis

Create your own analysis scripts:

```python
from validator_analyzer import ValidatorAnalyzer

# Initialize with custom settings
analyzer = ValidatorAnalyzer(use_cache=True, rate_limit_delay=0.3)

# Run custom analysis
results = analyzer.analyze_top_validators(limit=50, num_epochs=10)

# Generate custom reports
analyzer.generate_report(results, num_epochs=10)
stats = analyzer.get_validator_summary_stats(results)
```

## Configuration

### Environment Variables

- `SOLANA_RPC_URL`: Custom Solana RPC endpoint (default: mainnet-beta)

### Class Parameters

#### JitoClient
- `rate_limit_delay`: Delay between API requests (default: 0.5 seconds)
- `use_cache`: Enable file caching (default: True)

#### SolanaClient
- `rpc_url`: Solana RPC endpoint URL

#### ValidatorAnalyzer
- `use_cache`: Enable caching for API responses
- `rate_limit_delay`: Rate limiting for API calls

## File Structure

```
├── jito_mev_query.py      # Main entry point (refactored)
├── jito_client.py         # Jito API client
├── solana_client.py       # Solana RPC client
├── utils.py               # Shared utilities
├── validator_analyzer.py  # Main analysis logic
├── example_usage.py       # Usage examples
├── README.md              # This file
├── jsons/                 # Cache directory for API responses
└── jito_mev_report.json   # Generated analysis report
```

## Benefits of the New Architecture

1. **Separation of Concerns**: Jito and Solana functionality are cleanly separated
2. **Reusability**: Classes can be imported and used in other projects
3. **Testability**: Each class can be unit tested independently
4. **Extensibility**: Easy to add new features without affecting existing code
5. **Maintainability**: Changes to one API don't affect the other
6. **Flexibility**: Mix and match functionality as needed

## Dependencies

- `requests`: HTTP client for API calls
- `json`: JSON data handling (built-in)
- `time`: Rate limiting (built-in)
- `os`: File operations (built-in)

## Caching

The application uses file-based caching to avoid redundant API calls:
- Jito MEV rewards are cached in `jsons/rewards_{vote_account}.json`
- Cache files are automatically created and managed
- Set `use_cache=False` to disable caching

## Rate Limiting

Built-in rate limiting prevents API abuse:
- Default delay: 0.5 seconds between requests
- Configurable per client instance
- Respects API rate limits

## Error Handling

Robust error handling throughout:
- Network request failures
- API response errors
- Data parsing issues
- File I/O errors

## Future Enhancements

The modular architecture makes it easy to add:
- Additional Solana RPC functionality
- New analysis metrics
- Different data sources
- Enhanced reporting formats
- Real-time monitoring capabilities
