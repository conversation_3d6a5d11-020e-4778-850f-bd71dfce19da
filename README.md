# Solana Personal Delegator

A Python application for analyzing Solana validators and their MEV rewards through Jito APIs, with a clean separation between Jito and Solana functionality.

## Architecture

The codebase has been refactored into a modular, class-based architecture that separates concerns:

### Core Classes

#### 1. `JitoClient` (`jito_client.py`)
Handles all Jito-specific API interactions:
- Fetches validator lists from Jito APIs
- Retrieves MEV rewards data
- Implements rate limiting and caching
- Processes Jito-specific data formats

#### 2. `SolanaClient` (`solana_client.py`)
Manages direct Solana RPC calls:
- Connects to Solana RPC endpoints
- Fetches validator information from the blockchain
- Retrieves epoch and cluster data
- Handles Solana-specific data structures

#### 3. `Utils` (`utils.py`)
Provides shared utility functions:
- Lamports ↔ SOL conversions
- File caching operations
- Common data processing utilities

#### 4. `ValidatorAnalyzer` (`validator_analyzer.py`)
Main application logic and orchestration:
- Coordinates between Jito and Solana clients
- Processes and analyzes validator data
- Generates reports and statistics
- Manages the overall workflow

## Usage

### Basic Usage (Original Functionality)

Run the main analysis script:

```bash
python jito_mev_query.py
```

This will:
1. Fetch the top 100 Jito validators by stake
2. Retrieve MEV rewards for the last 5 epochs
3. Generate a detailed report with enhanced naming
4. Save results to `jito_mev_report_top100_5epochs.json`
5. Create cache files with validator names when available
6. Display summary statistics

### Advanced Usage (Individual Classes)

You can now use the classes independently for custom analysis:

```python
from jito_client import JitoClient
from solana_client import SolanaClient
from utils import Utils

# Use Jito client independently
jito = JitoClient(rate_limit_delay=0.5, use_cache=True)
validators = jito.get_validators(limit=10)
rewards = jito.get_mev_rewards("vote_account_address", num_epochs=3)

# Use Solana client independently
solana = SolanaClient()
epoch_info = solana.get_epoch_info()
vote_accounts = solana.get_vote_accounts()

# Use utilities
sol_amount = Utils.lamports_to_sol(1_500_000_000)  # Convert 1.5 SOL
Utils.save_cached_data("my_data.json", {"key": "value"})
```

See `example_usage.py` for more detailed examples.

### Custom Analysis

Create your own analysis scripts:

```python
from validator_analyzer import ValidatorAnalyzer

# Initialize with custom settings
analyzer = ValidatorAnalyzer(use_cache=True, rate_limit_delay=0.3)

# Run custom analysis
results = analyzer.analyze_top_validators(limit=50, num_epochs=10)

# Generate custom reports
analyzer.generate_report(results, num_epochs=10)
stats = analyzer.get_validator_summary_stats(results)
```

## Configuration

### Environment Variables

- `SOLANA_RPC_URL`: Custom Solana RPC endpoint (default: mainnet-beta)

### Class Parameters

#### JitoClient
- `rate_limit_delay`: Delay between API requests (default: 0.5 seconds)
- `use_cache`: Enable file caching (default: True)

#### SolanaClient
- `rpc_url`: Solana RPC endpoint URL

#### ValidatorAnalyzer
- `use_cache`: Enable caching for API responses
- `rate_limit_delay`: Rate limiting for API calls

## File Structure

```
├── jito_mev_query.py                    # Main entry point (refactored)
├── jito_client.py                       # Jito API client
├── solana_client.py                     # Solana RPC client
├── utils.py                             # Shared utilities
├── validator_analyzer.py                # Main analysis logic
├── example_usage.py                     # Usage examples
├── test_enhanced_naming.py              # Test script for enhanced naming
├── README.md                            # This file
├── jsons/                               # Cache directory for API responses
│   ├── rewards_ValidatorName_vote.json # Enhanced cache files with names
│   └── rewards_vote_account.json       # Fallback cache files
├── jito_mev_report_top100_5epochs.json # Generated analysis report
└── jito_mev_report.json                 # Legacy report file
```

## Enhanced Naming Features

### Report Files
- **Descriptive Naming**: Reports include analysis parameters in filename
- **Example**: `jito_mev_report_top100_5epochs.json` instead of generic names
- **Customizable**: Specify custom filenames or let the system generate descriptive ones

### Cache Files
- **Validator Names**: Include validator names in cache filenames when available
- **Smart Fallback**: Automatically falls back to vote account only when names unavailable
- **Backward Compatible**: Seamlessly works with existing cache files
- **Migration**: Automatically migrates old cache files to new naming scheme

### Filename Sanitization
- **Safe Characters**: Automatically converts unsafe filename characters
- **Length Limits**: Truncates overly long names to prevent filesystem issues
- **Consistent Format**: Standardized underscore-separated naming convention

## Benefits of the New Architecture

1. **Separation of Concerns**: Jito and Solana functionality are cleanly separated
2. **Reusability**: Classes can be imported and used in other projects
3. **Testability**: Each class can be unit tested independently
4. **Extensibility**: Easy to add new features without affecting existing code
5. **Maintainability**: Changes to one API don't affect the other
6. **Flexibility**: Mix and match functionality as needed
7. **Enhanced Organization**: Better file naming makes cache management easier
8. **Backward Compatibility**: Seamless migration from old naming schemes

## Dependencies

- `requests`: HTTP client for API calls
- `json`: JSON data handling (built-in)
- `time`: Rate limiting (built-in)
- `os`: File operations (built-in)

## Caching

The application uses file-based caching to avoid redundant API calls:
- **Enhanced Naming**: Cache files now include validator names when available
  - With name: `jsons/rewards_Solana_Foundation_{vote_account}.json`
  - Without name: `jsons/rewards_{vote_account}.json` (fallback)
- **Backward Compatibility**: Automatically migrates old cache files to new naming
- **Smart Fallback**: If new naming fails, falls back to old naming scheme
- Cache files are automatically created and managed
- Set `use_cache=False` to disable caching

### Cache File Examples
```
jsons/rewards_Solana_Foundation_DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m.json
jsons/rewards_Coinbase_Validator_CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB.json
jsons/rewards_he1iusunGwqrNtafDtLdhsUQDFvo13z9sUa36PauBtk.json  # No name available
```

## Rate Limiting

Built-in rate limiting prevents API abuse:
- Default delay: 0.5 seconds between requests
- Configurable per client instance
- Respects API rate limits

## Error Handling

Robust error handling throughout:
- Network request failures
- API response errors
- Data parsing issues
- File I/O errors

## Future Enhancements

The modular architecture makes it easy to add:
- Additional Solana RPC functionality
- New analysis metrics
- Different data sources
- Enhanced reporting formats
- Real-time monitoring capabilities
