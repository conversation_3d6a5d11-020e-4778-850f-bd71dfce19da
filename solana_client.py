import requests
import json
from typing import Dict, Any, Optional, List
from utils import Utils


class SolanaClient:
    """Client for interacting with Solana RPC endpoints."""
    
    def __init__(self, rpc_url: str = "https://api.mainnet-beta.solana.com"):
        """
        Initialize the Solana RPC client.
        
        Args:
            rpc_url: The Solana RPC endpoint URL
        """
        self.rpc_url = rpc_url
        self.headers = {
            "Content-Type": "application/json"
        }
    
    def get_validator_info(self, vote_account: str) -> Optional[Dict[str, Any]]:
        """
        Get validator information from Solana RPC.

        Args:
            vote_account: The validator's vote account address

        Returns:
            Validator information or None if not found
        """
        try:
            # Get vote account info
            params = [vote_account, {"encoding": "jsonParsed"}]
            response = self._make_rpc_call("getAccountInfo", params)

            if response and response.get("result", {}).get("value"):
                return response["result"]["value"]
            return None

        except Exception as e:
            print(f"Error fetching validator info for {vote_account}: {e}")
            return None

    def get_validator_name_from_vote_account(self, vote_account: str) -> Optional[str]:
        """
        Extract validator name from vote account data.

        Args:
            vote_account: The validator's vote account address

        Returns:
            Validator name if found, None otherwise
        """
        try:
            validator_info = self.get_validator_info(vote_account)
            if not validator_info:
                return None

            # Try to extract name from vote account data
            data = validator_info.get("data", {})
            if isinstance(data, dict):
                parsed = data.get("parsed", {})
                if isinstance(parsed, dict):
                    info = parsed.get("info", {})
                    if isinstance(info, dict):
                        # Look for validator name in various fields
                        name = (info.get("validator_name") or
                               info.get("name") or
                               info.get("validator") or
                               info.get("identity"))
                        if name and isinstance(name, str) and name.strip():
                            return name.strip()

            return None

        except Exception as e:
            print(f"Error extracting validator name for {vote_account}: {e}")
            return None

    def get_validator_name_from_cluster_nodes(self, vote_account: str) -> Optional[str]:
        """
        Get validator name from cluster nodes data.

        Args:
            vote_account: The validator's vote account address

        Returns:
            Validator name if found, None otherwise
        """
        try:
            cluster_nodes = self.get_cluster_nodes()
            if not cluster_nodes:
                return None

            # Look for the validator in cluster nodes
            for node in cluster_nodes:
                if node.get("pubkey") == vote_account:
                    # Extract name from gossip or other fields
                    gossip = node.get("gossip")
                    if gossip:
                        # Sometimes validator names are embedded in gossip addresses
                        # This is a simplified approach
                        pass

                    # Check if there's a name field
                    name = node.get("name") or node.get("validator_name")
                    if name and isinstance(name, str) and name.strip():
                        return name.strip()

            return None

        except Exception as e:
            print(f"Error getting validator name from cluster nodes for {vote_account}: {e}")
            return None
    
    def get_epoch_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current epoch information.
        
        Returns:
            Current epoch information or None if request failed
        """
        try:
            response = self._make_rpc_call("getEpochInfo", [])
            if response and "result" in response:
                return response["result"]
            return None
            
        except Exception as e:
            print(f"Error fetching epoch info: {e}")
            return None
    
    def get_stake_accounts(self, validator_vote_account: str) -> List[Dict[str, Any]]:
        """
        Get stake accounts delegated to a validator.
        
        Args:
            validator_vote_account: The validator's vote account address
            
        Returns:
            List of stake account information
        """
        try:
            # Get program accounts for stake program filtered by vote account
            params = [
                "Stake11111111111111111111111111111111111111",
                {
                    "encoding": "jsonParsed",
                    "filters": [
                        {
                            "memcmp": {
                                "offset": 124,  # Offset for vote account in stake account
                                "bytes": validator_vote_account
                            }
                        }
                    ]
                }
            ]
            
            response = self._make_rpc_call("getProgramAccounts", params)
            
            if response and "result" in response:
                return response["result"]
            return []
            
        except Exception as e:
            print(f"Error fetching stake accounts for {validator_vote_account}: {e}")
            return []
    
    def get_vote_accounts(self) -> Optional[Dict[str, Any]]:
        """
        Get all vote accounts and their voting status.
        
        Returns:
            Vote accounts information or None if request failed
        """
        try:
            response = self._make_rpc_call("getVoteAccounts", [])
            if response and "result" in response:
                return response["result"]
            return None
            
        except Exception as e:
            print(f"Error fetching vote accounts: {e}")
            return None
    
    def get_cluster_nodes(self) -> List[Dict[str, Any]]:
        """
        Get information about cluster nodes.
        
        Returns:
            List of cluster node information
        """
        try:
            response = self._make_rpc_call("getClusterNodes", [])
            if response and "result" in response:
                return response["result"]
            return []
            
        except Exception as e:
            print(f"Error fetching cluster nodes: {e}")
            return []
    
    def _make_rpc_call(self, method: str, params: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Make an RPC call to the Solana node.
        
        Args:
            method: The RPC method name
            params: List of parameters for the method
            
        Returns:
            RPC response or None if request failed
        """
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": method,
                "params": params
            }
            
            response = requests.post(
                self.rpc_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.RequestException as e:
            print(f"RPC call failed for method {method}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Failed to decode RPC response for method {method}: {e}")
            return None
