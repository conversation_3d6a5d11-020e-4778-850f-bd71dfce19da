import requests
import json
from typing import Dict, Any, Optional, List
from utils import Utils


class SolanaClient:
    """Client for interacting with Solana RPC endpoints."""
    
    def __init__(self, rpc_url: str = "https://api.mainnet-beta.solana.com"):
        """
        Initialize the Solana RPC client.
        
        Args:
            rpc_url: The Solana RPC endpoint URL
        """
        self.rpc_url = rpc_url
        self.headers = {
            "Content-Type": "application/json"
        }
    
    def get_validator_info(self, vote_account: str) -> Optional[Dict[str, Any]]:
        """
        Get validator information from Solana RPC.
        
        Args:
            vote_account: The validator's vote account address
            
        Returns:
            Validator information or None if not found
        """
        try:
            # Get vote account info
            params = [vote_account, {"encoding": "jsonParsed"}]
            response = self._make_rpc_call("getAccountInfo", params)
            
            if response and response.get("result", {}).get("value"):
                return response["result"]["value"]
            return None
            
        except Exception as e:
            print(f"Error fetching validator info for {vote_account}: {e}")
            return None
    
    def get_epoch_info(self) -> Optional[Dict[str, Any]]:
        """
        Get current epoch information.
        
        Returns:
            Current epoch information or None if request failed
        """
        try:
            response = self._make_rpc_call("getEpochInfo", [])
            if response and "result" in response:
                return response["result"]
            return None
            
        except Exception as e:
            print(f"Error fetching epoch info: {e}")
            return None
    
    def get_stake_accounts(self, validator_vote_account: str) -> List[Dict[str, Any]]:
        """
        Get stake accounts delegated to a validator.
        
        Args:
            validator_vote_account: The validator's vote account address
            
        Returns:
            List of stake account information
        """
        try:
            # Get program accounts for stake program filtered by vote account
            params = [
                "Stake11111111111111111111111111111111111111",
                {
                    "encoding": "jsonParsed",
                    "filters": [
                        {
                            "memcmp": {
                                "offset": 124,  # Offset for vote account in stake account
                                "bytes": validator_vote_account
                            }
                        }
                    ]
                }
            ]
            
            response = self._make_rpc_call("getProgramAccounts", params)
            
            if response and "result" in response:
                return response["result"]
            return []
            
        except Exception as e:
            print(f"Error fetching stake accounts for {validator_vote_account}: {e}")
            return []
    
    def get_vote_accounts(self) -> Optional[Dict[str, Any]]:
        """
        Get all vote accounts and their voting status.
        
        Returns:
            Vote accounts information or None if request failed
        """
        try:
            response = self._make_rpc_call("getVoteAccounts", [])
            if response and "result" in response:
                return response["result"]
            return None
            
        except Exception as e:
            print(f"Error fetching vote accounts: {e}")
            return None
    
    def get_cluster_nodes(self) -> List[Dict[str, Any]]:
        """
        Get information about cluster nodes.
        
        Returns:
            List of cluster node information
        """
        try:
            response = self._make_rpc_call("getClusterNodes", [])
            if response and "result" in response:
                return response["result"]
            return []
            
        except Exception as e:
            print(f"Error fetching cluster nodes: {e}")
            return []
    
    def _make_rpc_call(self, method: str, params: List[Any]) -> Optional[Dict[str, Any]]:
        """
        Make an RPC call to the Solana node.
        
        Args:
            method: The RPC method name
            params: List of parameters for the method
            
        Returns:
            RPC response or None if request failed
        """
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": method,
                "params": params
            }
            
            response = requests.post(
                self.rpc_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.RequestException as e:
            print(f"RPC call failed for method {method}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"Failed to decode RPC response for method {method}: {e}")
            return None
