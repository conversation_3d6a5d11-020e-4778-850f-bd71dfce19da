{"DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m": {"name": "Kiln", "source": "known_mapping", "vote_account": "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m"}, "CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB": {"name": "Coinbase", "source": "known_mapping", "vote_account": "CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB"}, "HZKopZYvv8v6un2H6KUNVQCnK5zM9emKKezvqhTBSpEc": {"name": "Binance Staking", "source": "known_mapping", "vote_account": "HZKopZYvv8v6un2H6KUNVQCnK5zM9emKKezvqhTBSpEc"}, "3N7s9zXMZ4QqvHQR15t5GNHyqc89KduzMP7423eWiD5g": {"name": "Figment", "source": "known_mapping", "vote_account": "3N7s9zXMZ4QqvHQR15t5GNHyqc89KduzMP7423eWiD5g"}, "CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1": {"name": "Chorus One", "source": "known_mapping", "vote_account": "CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1"}, "he1iusunGwqrNtafDtLdhsUQDFvo13z9sUa36PauBtk": {"name": "<PERSON><PERSON>", "source": "known_mapping", "vote_account": "he1iusunGwqrNtafDtLdhsUQDFvo13z9sUa36PauBtk"}, "26pV97Ce83ZQ6Kz9XT4td8tdoUFPTng8Fb8gPyc53dJx": {"name": "Solana Foundation", "source": "known_mapping", "vote_account": "26pV97Ce83ZQ6Kz9XT4td8tdoUFPTng8Fb8gPyc53dJx"}, "CatzoSMUkTRidT5DwBxAC2pEtnwMBTpkCepHkFgZDiqb": {"name": "<PERSON><PERSON>", "source": "known_mapping", "vote_account": "CatzoSMUkTRidT5DwBxAC2pEtnwMBTpkCepHkFgZDiqb"}, "6D2jqw9hyVCpppZexquxa74Fn33rJzzBx38T58VucHx9": {"name": "<PERSON><PERSON>", "source": "known_mapping", "vote_account": "6D2jqw9hyVCpppZexquxa74Fn33rJzzBx38T58VucHx9"}, "DumiCKHVqoCQKD8roLApzR5Fit8qGV5fVQsJV9sTZk4a": {"name": "<PERSON><PERSON>", "source": "known_mapping", "vote_account": "DumiCKHVqoCQKD8roLApzR5Fit8qGV5fVQsJV9sTZk4a"}, "9QU2QSxhb24FUX3Tu2FpczXjpK3VYrvRudywSZaM29mF": {"name": "Shinobi Systems", "source": "known_mapping", "vote_account": "9QU2QSxhb24FUX3Tu2FpczXjpK3VYrvRudywSZaM29mF"}, "KRAKEnMdmT4EfM8ykTFH6yLoCd5vNLcQvJwF66Y2dag": {"name": "<PERSON><PERSON><PERSON>", "source": "known_mapping", "vote_account": "KRAKEnMdmT4EfM8ykTFH6yLoCd5vNLcQvJwF66Y2dag"}, "BLADE1qNA1uNjRgER6DtUFf7FU3c1TWLLdpPeEcKatZ2": {"name": "Blade", "source": "known_mapping", "vote_account": "BLADE1qNA1uNjRgER6DtUFf7FU3c1TWLLdpPeEcKatZ2"}, "Chorus6Kis8tFHA7AowrPMcRJk3LbApHTYpgSNXzY5KE": {"name": "Chorus One", "source": "known_mapping", "vote_account": "Chorus6Kis8tFHA7AowrPMcRJk3LbApHTYpgSNXzY5KE"}, "CertusDeBmqN8ZawdkxK5kFGMwBXdudvWHYwtNgNhvLu": {"name": "Certus One", "source": "known_mapping", "vote_account": "CertusDeBmqN8ZawdkxK5kFGMwBXdudvWHYwtNgNhvLu"}, "DriFTm3wM9ugxhCA1K3wVQMSdC4Dv4LNmyZMmZiuHRpp": {"name": "Drift", "source": "known_mapping", "vote_account": "DriFTm3wM9ugxhCA1K3wVQMSdC4Dv4LNmyZMmZiuHRpp"}, "EARNynHRWg6GfyJCmrrizcZxARB3HVzcaasvNa8kBS72": {"name": "<PERSON><PERSON><PERSON>", "source": "known_mapping", "vote_account": "EARNynHRWg6GfyJCmrrizcZxARB3HVzcaasvNa8kBS72"}, "LSTmLs1DENX82ihc7jU134mydiV3NDEPXsRrekAY6Ys": {"name": "Lido", "source": "known_mapping", "vote_account": "LSTmLs1DENX82ihc7jU134mydiV3NDEPXsRrekAY6Ys"}}