#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced naming functionality with mock validator data.
This shows how the system handles validators with actual names vs. unknown validators.
"""

from jito_client import JitoClient
from validator_analyzer import ValidatorAnalyzer
from utils import Utils
import json


def test_enhanced_naming():
    """Test the enhanced naming functionality with mock data."""
    print("=== Testing Enhanced Naming Functionality ===\n")
    
    # Test 1: Utils filename generation
    print("1. Testing Utils filename generation:")
    
    # Test with validator name
    filename_with_name = Utils.get_cache_filename(
        "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m", 
        "rewards", 
        "Solana Foundation"
    )
    print(f"   With name: {filename_with_name}")
    
    # Test without validator name
    filename_without_name = Utils.get_cache_filename(
        "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m", 
        "rewards"
    )
    print(f"   Without name: {filename_without_name}")
    
    # Test with special characters in name
    filename_special_chars = Utils.get_cache_filename(
        "vote123", 
        "rewards", 
        "Validator/Name<>With:Special*Chars"
    )
    print(f"   With special chars: {filename_special_chars}")
    
    print()
    
    # Test 2: Report filename generation
    print("2. Testing report filename generation:")
    
    report_filename = Utils.generate_report_filename(
        base_name="jito_mev_report",
        limit=50,
        num_epochs=10,
        extension="json"
    )
    print(f"   Report filename: {report_filename}")
    
    # Test with different parameters
    report_filename_2 = Utils.generate_report_filename(
        base_name="validator_analysis",
        limit=25,
        num_epochs=3,
        extension="csv"
    )
    print(f"   Custom report: {report_filename_2}")
    
    print()
    
    # Test 3: Filename sanitization
    print("3. Testing filename sanitization:")
    
    test_names = [
        "Solana Foundation",
        "Validator/Name<>With:Special*Chars",
        "Name with spaces and symbols!@#$%",
        "Very_Long_Validator_Name_That_Exceeds_Normal_Length_Limits_And_Should_Be_Truncated",
        None,
        "",
        "none"
    ]
    
    for name in test_names:
        sanitized = Utils.sanitize_filename(name)
        print(f"   '{name}' -> '{sanitized}'")
    
    print()
    
    # Test 4: Cache file listing
    print("4. Testing cache file operations:")
    
    cache_files = Utils.list_cache_files()
    print(f"   Found {len(cache_files)} cache files")
    if cache_files:
        print(f"   First few: {cache_files[:3]}")
    
    print()
    
    # Test 5: Mock validator analysis with names
    print("5. Testing mock validator analysis with names:")
    
    # Create mock validator data with names
    mock_validators = [
        {
            "identity": "Solana Foundation",
            "vote_account": "test_vote_1",
            "active_stake": *************,
            "mev_commission_bps": 500
        },
        {
            "identity": "Coinbase Validator",
            "vote_account": "test_vote_2", 
            "active_stake": ************,
            "mev_commission_bps": 1000
        },
        {
            "identity": None,  # Unknown validator
            "vote_account": "test_vote_3",
            "active_stake": ************,
            "mev_commission_bps": 0
        }
    ]
    
    # Test filename generation for each
    for i, validator in enumerate(mock_validators, 1):
        identity = validator.get('identity')
        vote_account = validator.get('vote_account')
        
        cache_filename = Utils.get_cache_filename(vote_account, "rewards", identity)
        display_name = identity if identity else "Unknown"
        
        print(f"   Validator {i}: {display_name}")
        print(f"     Cache file: {cache_filename}")
        
        # Test saving mock data
        mock_rewards_data = [
            {"epoch": 843, "mev_rewards": ***********, "mev_commission_bps": validator['mev_commission_bps']},
            {"epoch": 842, "mev_rewards": ***********, "mev_commission_bps": validator['mev_commission_bps']}
        ]
        
        success = Utils.save_cached_data(cache_filename, mock_rewards_data)
        print(f"     Saved mock data: {success}")
    
    print()
    
    # Test 6: Demonstrate backward compatibility
    print("6. Testing backward compatibility:")
    
    # Create a file with old naming scheme
    old_filename = Utils.get_cache_filename("test_vote_old", "rewards")
    old_data = [{"epoch": 843, "mev_rewards": ***********}]
    Utils.save_cached_data(old_filename, old_data)
    print(f"   Created old-style cache: {old_filename}")
    
    # Try to load with new naming scheme (should fall back to old)
    new_filename = Utils.get_cache_filename("test_vote_old", "rewards", "Test Validator")
    loaded_data = Utils.load_cached_data(new_filename)
    if not loaded_data:
        loaded_data = Utils.load_cached_data(old_filename)
        print(f"   Fell back to old naming: {old_filename}")
    else:
        print(f"   Loaded from new naming: {new_filename}")
    
    print(f"   Data loaded successfully: {loaded_data is not None}")
    
    print()
    
    # Test 7: Show current cache directory contents
    print("7. Current cache directory contents:")
    cache_files = Utils.list_cache_files()
    
    # Group by naming scheme
    old_style = []
    new_style = []
    
    for filename in cache_files:
        if filename.count('_') == 2:  # rewards_voteaccount.json
            old_style.append(filename)
        elif filename.count('_') >= 3:  # rewards_name_voteaccount.json
            new_style.append(filename)
    
    print(f"   Old naming scheme: {len(old_style)} files")
    if old_style:
        print(f"     Examples: {old_style[:2]}")
    
    print(f"   New naming scheme: {len(new_style)} files")
    if new_style:
        print(f"     Examples: {new_style[:2]}")
    
    print("\n=== Enhanced Naming Test Complete ===")


if __name__ == "__main__":
    test_enhanced_naming()
