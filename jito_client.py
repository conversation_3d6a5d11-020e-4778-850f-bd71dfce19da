import requests
import time
from typing import List, Dict, Any, Optional
from utils import Utils


class JitoClient:
    """Client for interacting with Jito APIs."""
    
    # Jito API endpoints
    VALIDATORS_API = "https://kobe.mainnet.jito.network/api/v1/validators"
    REWARDS_API = "https://kobe.mainnet.jito.network/api/v1/validators/"
    
    def __init__(self, rate_limit_delay: float = 0.5, use_cache: bool = True):
        """
        Initialize the Jito client.
        
        Args:
            rate_limit_delay: Delay between API requests in seconds
            use_cache: Whether to use file caching for API responses
        """
        self.rate_limit_delay = rate_limit_delay
        self.use_cache = use_cache
    
    def get_validators(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Fetch the list of Jito validators, sorted by stake.
        
        Args:
            limit: Maximum number of validators to return
            
        Returns:
            List of validator dictionaries sorted by stake (descending)
        """
        try:
            response = self._make_request(self.VALIDATORS_API)
            if not response:
                return []
            
            validators_data = response.json()
            
            # Sort validators by stake amount in descending order
            sorted_validators = sorted(
                validators_data['validators'], 
                key=lambda x: x.get('active_stake', 0), 
                reverse=True
            )
            
            # Return the top N validators
            return sorted_validators[:limit]
            
        except (requests.RequestException, KeyError, ValueError) as e:
            print(f"Error fetching validators: {e}")
            return []
    
    def get_mev_rewards(self, vote_account: str, num_epochs: int = 5) -> float:
        """
        Fetch MEV rewards for a specific validator's vote account.
        
        Args:
            vote_account: The validator's vote account address
            num_epochs: Number of recent epochs to retrieve rewards for
            
        Returns:
            Total MEV rewards in SOL for the specified epochs
        """
        try:
            # Check cache first if enabled
            if self.use_cache:
                cache_filename = Utils.get_cache_filename(vote_account, "rewards")
                cached_data = Utils.load_cached_data(cache_filename)
                if cached_data:
                    return self._process_rewards_data(cached_data, num_epochs)
            
            # Fetch from API
            url = f"{self.REWARDS_API}{vote_account}"
            response = self._make_request(url)
            if not response:
                return 0.0
            
            # Add rate limiting
            time.sleep(self.rate_limit_delay)
            
            rewards_data = response.json()
            
            # Save to cache if enabled
            if self.use_cache:
                cache_filename = Utils.get_cache_filename(vote_account, "rewards")
                Utils.save_cached_data(cache_filename, rewards_data)
            
            return self._process_rewards_data(rewards_data, num_epochs)
            
        except (requests.RequestException, KeyError, ValueError) as e:
            print(f"Error fetching MEV rewards for {vote_account}: {e}")
            return 0.0
    
    def _make_request(self, url: str) -> Optional[requests.Response]:
        """
        Make an HTTP request with error handling.
        
        Args:
            url: The URL to request
            
        Returns:
            Response object or None if request failed
        """
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"Request failed for {url}: {e}")
            return None
    
    def _process_rewards_data(self, rewards_data: List[Dict[str, Any]], num_epochs: int) -> float:
        """
        Process rewards data and calculate total MEV rewards in SOL.
        
        Args:
            rewards_data: List of reward data for epochs
            num_epochs: Number of recent epochs to process
            
        Returns:
            Total MEV rewards in SOL
        """
        try:
            # Take rewards for the specified number of recent epochs
            recent_rewards = rewards_data[:num_epochs]
            
            # Sum up MEV rewards in lamports
            total_mev_lamports = sum(item.get('mev_rewards', 0) for item in recent_rewards)
            
            # Convert lamports to SOL
            return Utils.lamports_to_sol(total_mev_lamports)
            
        except (TypeError, KeyError) as e:
            print(f"Error processing rewards data: {e}")
            return 0.0
