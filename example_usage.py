#!/usr/bin/env python3
"""
Example script demonstrating how to use the separated Jito and Solana client classes.
This shows the flexibility and reusability of the new class-based architecture.
"""

from jito_client import JitoClient
from solana_client import SolanaClient
from utils import Utils


def example_jito_usage():
    """Example of using JitoClient independently."""
    print("=== Jito Client Example ===")
    
    # Initialize Jito client
    jito = JitoClient(rate_limit_delay=0.1, use_cache=True)
    
    # Get top 5 validators
    print("Fetching top 5 Jito validators...")
    validators = jito.get_validators(limit=5)
    
    if validators:
        print(f"Found {len(validators)} validators:")
        for i, validator in enumerate(validators, 1):
            vote_account = validator.get('vote_account')
            identity = validator.get('identity', 'Unknown')
            stake = validator.get('active_stake', 0)
            
            print(f"  {i}. {identity} ({vote_account})")
            print(f"     Stake: {Utils.lamports_to_sol(stake):.2f} SOL")
            
            # Get MEV rewards for this validator
            mev_rewards = jito.get_mev_rewards(vote_account, num_epochs=3)
            print(f"     MEV Rewards (last 3 epochs): {mev_rewards:.6f} SOL")
            print()
    else:
        print("No validators found.")


def example_solana_usage():
    """Example of using SolanaClient independently."""
    print("=== Solana Client Example ===")
    
    # Initialize Solana client
    solana = SolanaClient()
    
    # Get current epoch info
    print("Fetching current epoch information...")
    epoch_info = solana.get_epoch_info()
    
    if epoch_info:
        print(f"Current Epoch: {epoch_info.get('epoch', 'Unknown')}")
        print(f"Slot Height: {epoch_info.get('slotIndex', 'Unknown')}")
        print(f"Slots in Epoch: {epoch_info.get('slotsInEpoch', 'Unknown')}")
        print()
    else:
        print("Failed to fetch epoch information.")
    
    # Get vote accounts (this might take a while)
    print("Fetching vote accounts summary...")
    vote_accounts = solana.get_vote_accounts()
    
    if vote_accounts:
        current = vote_accounts.get('current', [])
        delinquent = vote_accounts.get('delinquent', [])
        
        print(f"Current validators: {len(current)}")
        print(f"Delinquent validators: {len(delinquent)}")
        
        if current:
            print("\nTop 3 validators by stake:")
            # Sort by activated stake
            sorted_validators = sorted(current, key=lambda x: x.get('activatedStake', 0), reverse=True)
            for i, validator in enumerate(sorted_validators[:3], 1):
                vote_pubkey = validator.get('votePubkey', 'Unknown')
                node_pubkey = validator.get('nodePubkey', 'Unknown')
                stake = validator.get('activatedStake', 0)
                commission = validator.get('commission', 0)
                
                print(f"  {i}. Vote Account: {vote_pubkey}")
                print(f"     Node: {node_pubkey}")
                print(f"     Stake: {Utils.lamports_to_sol(stake):.2f} SOL")
                print(f"     Commission: {commission}%")
                print()
    else:
        print("Failed to fetch vote accounts.")


def example_utils_usage():
    """Example of using Utils class."""
    print("=== Utils Example ===")
    
    # Conversion examples
    lamports = 1_500_000_000  # 1.5 SOL in lamports
    sol = Utils.lamports_to_sol(lamports)
    print(f"{lamports:,} lamports = {sol} SOL")
    
    sol_amount = 2.5
    lamports_converted = Utils.sol_to_lamports(sol_amount)
    print(f"{sol_amount} SOL = {lamports_converted:,} lamports")
    
    # Cache example
    test_data = {"example": "data", "timestamp": "2024-01-01"}
    filename = "test_cache.json"
    
    print(f"\nSaving test data to cache: {filename}")
    success = Utils.save_cached_data(filename, test_data)
    print(f"Save successful: {success}")
    
    print(f"Loading test data from cache: {filename}")
    loaded_data = Utils.load_cached_data(filename)
    print(f"Loaded data: {loaded_data}")
    
    print()


def main():
    """Main function demonstrating all examples."""
    print("Solana Personal Delegator - Class Usage Examples")
    print("=" * 50)
    print()
    
    # Run examples
    example_utils_usage()
    example_jito_usage()
    example_solana_usage()
    
    print("=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    main()
