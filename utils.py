import os
import json
from typing import Any, Optional


class Utils:
    """Utility functions for file operations and data conversions."""
    
    LAMPORTS_PER_SOL = 1_000_000_000
    DEFAULT_CACHE_DIR = "jsons"
    
    @staticmethod
    def lamports_to_sol(lamports: int) -> float:
        """Convert lamports to SOL."""
        return lamports / Utils.LAMPORTS_PER_SOL
    
    @staticmethod
    def sol_to_lamports(sol: float) -> int:
        """Convert SOL to lamports."""
        return int(sol * Utils.LAMPORTS_PER_SOL)
    
    @staticmethod
    def ensure_cache_directory(cache_dir: str = DEFAULT_CACHE_DIR) -> None:
        """Ensure the cache directory exists."""
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
    
    @staticmethod
    def load_cached_data(filename: str, cache_dir: str = DEFAULT_CACHE_DIR) -> Optional[Any]:
        """Load data from JSON cache file."""
        try:
            Utils.ensure_cache_directory(cache_dir)
            filepath = os.path.join(cache_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, "r") as f:
                    return json.load(f)
            return None
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading cached data from {filename}: {e}")
            return None
    
    @staticmethod
    def save_cached_data(filename: str, data: Any, cache_dir: str = DEFAULT_CACHE_DIR) -> bool:
        """Save data to JSON cache file."""
        try:
            Utils.ensure_cache_directory(cache_dir)
            filepath = os.path.join(cache_dir, filename)
            with open(filepath, "w") as f:
                json.dump(data, f, indent=4)
            return True
        except (IOError, TypeError) as e:
            print(f"Error saving cached data to {filename}: {e}")
            return False
    
    @staticmethod
    def get_cache_filename(vote_account: str, prefix: str = "rewards") -> str:
        """Generate a cache filename for a vote account."""
        return f"{prefix}_{vote_account}.json"
