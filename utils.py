import os
import json
from typing import Any, Optional, List


class Utils:
    """Utility functions for file operations and data conversions."""
    
    LAMPORTS_PER_SOL = 1_000_000_000
    DEFAULT_CACHE_DIR = "jsons"
    
    @staticmethod
    def lamports_to_sol(lamports: int) -> float:
        """Convert lamports to SOL."""
        return lamports / Utils.LAMPORTS_PER_SOL
    
    @staticmethod
    def sol_to_lamports(sol: float) -> int:
        """Convert SOL to lamports."""
        return int(sol * Utils.LAMPORTS_PER_SOL)
    
    @staticmethod
    def ensure_cache_directory(cache_dir: str = DEFAULT_CACHE_DIR) -> None:
        """Ensure the cache directory exists."""
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
    
    @staticmethod
    def load_cached_data(filename: str, cache_dir: str = DEFAULT_CACHE_DIR) -> Optional[Any]:
        """Load data from JSON cache file."""
        try:
            Utils.ensure_cache_directory(cache_dir)
            filepath = os.path.join(cache_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, "r") as f:
                    return json.load(f)
            return None
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading cached data from {filename}: {e}")
            return None
    
    @staticmethod
    def save_cached_data(filename: str, data: Any, cache_dir: str = DEFAULT_CACHE_DIR) -> bool:
        """Save data to JSON cache file."""
        try:
            Utils.ensure_cache_directory(cache_dir)
            filepath = os.path.join(cache_dir, filename)
            with open(filepath, "w") as f:
                json.dump(data, f, indent=4)
            return True
        except (IOError, TypeError) as e:
            print(f"Error saving cached data to {filename}: {e}")
            return False
    
    @staticmethod
    def get_cache_filename(vote_account: str, prefix: str = "rewards", validator_name: str = None) -> str:
        """
        Generate a cache filename for a vote account with optional validator name.

        Args:
            vote_account: The validator's vote account address
            prefix: Filename prefix (default: "rewards")
            validator_name: Optional validator name/identity

        Returns:
            Formatted filename with validator name if available
        """
        if validator_name:
            # Sanitize validator name for filename
            safe_name = Utils.sanitize_filename(validator_name)
            return f"{prefix}_{safe_name}_{vote_account}.json"
        else:
            return f"{prefix}_{vote_account}.json"

    @staticmethod
    def sanitize_filename(name: str) -> str:
        """
        Sanitize a string to be safe for use in filenames.

        Args:
            name: The string to sanitize

        Returns:
            Sanitized string safe for filenames
        """
        if not name or name.lower() == 'none':
            return "unknown"

        # Replace invalid filename characters with underscores
        invalid_chars = '<>:"/\\|?*'
        sanitized = name
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')

        # Replace spaces with underscores and limit length
        sanitized = sanitized.replace(' ', '_').strip('_')

        # Limit length to avoid filesystem issues
        if len(sanitized) > 50:
            sanitized = sanitized[:50].rstrip('_')

        return sanitized if sanitized else "unknown"

    @staticmethod
    def generate_report_filename(base_name: str = "jito_mev_report",
                                limit: int = None,
                                num_epochs: int = None,
                                extension: str = "json") -> str:
        """
        Generate a descriptive report filename with analysis parameters.

        Args:
            base_name: Base name for the report file
            limit: Number of validators analyzed
            num_epochs: Number of epochs analyzed
            extension: File extension (default: "json")

        Returns:
            Descriptive filename with parameters
        """
        filename_parts = [base_name]

        if limit:
            filename_parts.append(f"top{limit}")

        if num_epochs:
            filename_parts.append(f"{num_epochs}epochs")

        filename = "_".join(filename_parts)
        return f"{filename}.{extension}"

    @staticmethod
    def list_cache_files(cache_dir: str = DEFAULT_CACHE_DIR, prefix: str = "rewards") -> List[str]:
        """
        List all cache files with the given prefix.

        Args:
            cache_dir: Cache directory to search
            prefix: File prefix to filter by

        Returns:
            List of cache filenames
        """
        try:
            Utils.ensure_cache_directory(cache_dir)
            files = os.listdir(cache_dir)
            return [f for f in files if f.startswith(prefix) and f.endswith('.json')]
        except OSError:
            return []

    @staticmethod
    def get_validator_name_from_cache_filename(filename: str) -> str:
        """
        Extract validator name from cache filename if present.

        Args:
            filename: Cache filename to parse

        Returns:
            Validator name or "unknown" if not found
        """
        # Remove extension
        name_part = filename.replace('.json', '')

        # Split by underscores
        parts = name_part.split('_')

        # If format is prefix_name_voteaccount, extract name
        if len(parts) >= 3:
            # Join all parts except first (prefix) and last (vote account)
            validator_name = '_'.join(parts[1:-1])
            return validator_name if validator_name != "unknown" else "Unknown"

        return "Unknown"
