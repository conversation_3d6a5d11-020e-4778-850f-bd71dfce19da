import json
from typing import List, Dict, Any
from jito_client import <PERSON>toC<PERSON>
from solana_client import SolanaClient


class ValidatorAnalyzer:
    """Main class for analyzing validator performance and MEV rewards."""
    
    def __init__(self, use_cache: bool = True, rate_limit_delay: float = 0.5):
        """
        Initialize the validator analyzer.
        
        Args:
            use_cache: Whether to use file caching for API responses
            rate_limit_delay: Delay between API requests in seconds
        """
        self.jito_client = JitoClient(rate_limit_delay=rate_limit_delay, use_cache=use_cache)
        self.solana_client = SolanaClient()  # For future Solana RPC functionality
    
    def analyze_top_validators(self, limit: int = 100, num_epochs: int = 5) -> List[Dict[str, Any]]:
        """
        Analyze top validators by stake and fetch their MEV rewards.
        
        Args:
            limit: Number of top validators to analyze
            num_epochs: Number of recent epochs to analyze for MEV rewards
            
        Returns:
            List of validator analysis results
        """
        print(f"Fetching top {limit} Jito validators...")
        top_validators = self.jito_client.get_validators(limit=limit)
        
        if not top_validators:
            print("Failed to fetch validator data. Exiting.")
            return []
        
        results = []
        print("Processing validators and fetching MEV rewards...")
        
        for i, validator in enumerate(top_validators):
            vote_account = validator.get('vote_account')
            identity = validator.get('identity')
            
            if vote_account:
                # Try to get a meaningful validator name
                validator_name = self._get_validator_display_name(identity, vote_account)

                # Query MEV rewards with enhanced validator name for better cache naming
                mev_rewards_sol = self.jito_client.get_mev_rewards(
                    vote_account,
                    num_epochs=num_epochs,
                    validator_name=validator_name
                )

                # Try to get a meaningful validator name
                validator_name = self._get_validator_display_name(identity, vote_account)

                results.append({
                    "rank": i + 1,
                    "identity": identity,  # Keep original for compatibility
                    "validator_name": validator_name,  # Enhanced name field
                    "vote_account": vote_account,
                    "total_mev_rewards_sol_last_epochs": mev_rewards_sol,
                    "commission_bps": validator.get('mev_commission_bps', 'N/A'),
                    "active_stake": validator.get('active_stake', 0)
                })

                # Print progress with enhanced display name
                print(f"  [{i + 1}/{limit}] Fetched rewards for {validator_name} ({vote_account})")
        
        # Sort results by MEV rewards
        sorted_results = sorted(results, key=lambda x: x['total_mev_rewards_sol_last_epochs'], reverse=True)
        
        return sorted_results
    
    def generate_report(self, results: List[Dict[str, Any]], num_epochs: int = 5) -> None:
        """
        Generate and display the MEV rewards report.
        
        Args:
            results: List of validator analysis results
            num_epochs: Number of epochs analyzed
        """
        if not results:
            print("No results to display.")
            return
        
        print(f"\n--- MEV Rewards Report (Top {len(results)} Jito Validators) ---")
        print(f"Total MEV rewards shown for the last {num_epochs} epochs.")
        print("-" * 70)
        
        for res in results:
            commission_display = (
                f"{res['commission_bps'] / 100}%"
                if res['commission_bps'] != 'N/A'
                else 'N/A'
            )

            # Use the enhanced validator_name field for display
            display_name = res.get('validator_name', res.get('identity', 'Unknown'))

            print(
                f"Rank {res['rank']}: {display_name} ({res['vote_account']})\n"
                f"  -> MEV Rewards: {res['total_mev_rewards_sol_last_epochs']:.6f} SOL\n"
                f"  -> MEV Commission: {commission_display}\n"
            )
        
        print("-" * 70)
    
    def save_report(self, results: List[Dict[str, Any]],
                   filename: str = None,
                   limit: int = None,
                   num_epochs: int = None) -> bool:
        """
        Save the analysis report to a JSON file with descriptive naming.

        Args:
            results: List of validator analysis results
            filename: Custom filename (if None, generates descriptive name)
            limit: Number of validators analyzed (for filename generation)
            num_epochs: Number of epochs analyzed (for filename generation)

        Returns:
            True if saved successfully, False otherwise
        """
        from utils import Utils

        # Generate descriptive filename if not provided
        if filename is None:
            filename = Utils.generate_report_filename(
                base_name="jito_mev_report",
                limit=limit,
                num_epochs=num_epochs,
                extension="json"
            )

        try:
            with open(filename, "w") as f:
                json.dump(results, f, indent=4)
            print(f"\nReport saved to {filename}")
            return True
        except IOError as e:
            print(f"Error saving report to {filename}: {e}")
            return False
    
    def get_validator_summary_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
            
        Returns:
            Dictionary containing summary statistics
        """
        if not results:
            return {}
        
        mev_rewards = [r['total_mev_rewards_sol_last_epochs'] for r in results]
        commissions = [r['commission_bps'] for r in results if r['commission_bps'] != 'N/A']
        
        stats = {
            "total_validators": len(results),
            "total_mev_rewards_sol": sum(mev_rewards),
            "average_mev_rewards_sol": sum(mev_rewards) / len(mev_rewards) if mev_rewards else 0,
            "max_mev_rewards_sol": max(mev_rewards) if mev_rewards else 0,
            "min_mev_rewards_sol": min(mev_rewards) if mev_rewards else 0,
            "average_commission_bps": sum(commissions) / len(commissions) if commissions else 0,
            "validators_with_zero_commission": len([c for c in commissions if c == 0]),
        }
        
        return stats
    
    def print_summary_stats(self, results: List[Dict[str, Any]]) -> None:
        """
        Print summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
        """
        stats = self.get_validator_summary_stats(results)
        
        if not stats:
            print("No statistics available.")
            return
        
        print("\n--- Summary Statistics ---")
        print(f"Total Validators Analyzed: {stats['total_validators']}")
        print(f"Total MEV Rewards: {stats['total_mev_rewards_sol']:.6f} SOL")
        print(f"Average MEV Rewards: {stats['average_mev_rewards_sol']:.6f} SOL")
        print(f"Max MEV Rewards: {stats['max_mev_rewards_sol']:.6f} SOL")
        print(f"Min MEV Rewards: {stats['min_mev_rewards_sol']:.6f} SOL")
        print(f"Average Commission: {stats['average_commission_bps']:.2f} bps")
        print(f"Validators with 0% Commission: {stats['validators_with_zero_commission']}")
        print("-" * 30)

    def _get_validator_display_name(self, identity: str, vote_account: str) -> str:
        """
        Get a meaningful display name for a validator.

        Args:
            identity: The validator's identity from Jito API (may be null)
            vote_account: The validator's vote account address

        Returns:
            A meaningful display name for the validator
        """
        # If we have an identity from Jito, use it
        if identity and identity.lower() != 'none':
            return identity

        # Try to get validator info from Solana RPC
        try:
            # Check if we have a known validator mapping
            known_validators = self._get_known_validator_mapping()
            if vote_account in known_validators:
                return known_validators[vote_account]

            # For now, create a shortened version of the vote account
            # In the future, this could query Solana RPC for validator info
            return f"Validator-{vote_account[:8]}...{vote_account[-4:]}"

        except Exception:
            # Fallback to shortened vote account
            return f"Validator-{vote_account[:8]}...{vote_account[-4:]}"

    def _get_known_validator_mapping(self) -> dict:
        """
        Get a mapping of known validator vote accounts to their names.
        This can be expanded with a database or external service in the future.

        Returns:
            Dictionary mapping vote accounts to validator names
        """
        # This is a small sample of known validators
        # In a production system, this could be loaded from a database or API
        return {
            "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m": "Unknown Validator #1",
            "CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB": "Unknown Validator #2",
            "HZKopZYvv8v6un2H6KUNVQCnK5zM9emKKezvqhTBSpEc": "Unknown Validator #3",
            "3N7s9zXMZ4QqvHQR15t5GNHyqc89KduzMP7423eWiD5g": "Unknown Validator #4",
            "CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1": "Unknown Validator #5",
            # Add more known validators here
        }
