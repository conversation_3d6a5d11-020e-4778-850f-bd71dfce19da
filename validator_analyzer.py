import json
from typing import List, Dict, Any
from jito_client import JitoC<PERSON>
from solana_client import SolanaClient


class ValidatorAnalyzer:
    """Main class for analyzing validator performance and MEV rewards."""
    
    def __init__(self, use_cache: bool = True, rate_limit_delay: float = 0.5):
        """
        Initialize the validator analyzer.
        
        Args:
            use_cache: Whether to use file caching for API responses
            rate_limit_delay: Delay between API requests in seconds
        """
        self.jito_client = JitoClient(rate_limit_delay=rate_limit_delay, use_cache=use_cache)
        self.solana_client = SolanaClient()  # For future Solana RPC functionality
    
    def analyze_top_validators(self, limit: int = 100, num_epochs: int = 5) -> List[Dict[str, Any]]:
        """
        Analyze top validators by stake and fetch their MEV rewards.
        
        Args:
            limit: Number of top validators to analyze
            num_epochs: Number of recent epochs to analyze for MEV rewards
            
        Returns:
            List of validator analysis results
        """
        print(f"Fetching top {limit} Jito validators...")
        top_validators = self.jito_client.get_validators(limit=limit)
        
        if not top_validators:
            print("Failed to fetch validator data. Exiting.")
            return []
        
        results = []
        print("Processing validators and fetching MEV rewards...")
        
        for i, validator in enumerate(top_validators):
            vote_account = validator.get('vote_account')
            identity = validator.get('identity')
            
            if vote_account:
                # Query MEV rewards
                mev_rewards_sol = self.jito_client.get_mev_rewards(vote_account, num_epochs=num_epochs)
                
                results.append({
                    "rank": i + 1,
                    "identity": identity,
                    "vote_account": vote_account,
                    "total_mev_rewards_sol_last_epochs": mev_rewards_sol,
                    "commission_bps": validator.get('mev_commission_bps', 'N/A'),
                    "active_stake": validator.get('active_stake', 0)
                })
                
                # Print progress
                print(f"  [{i + 1}/{limit}] Fetched rewards for {identity} ({vote_account})")
        
        # Sort results by MEV rewards
        sorted_results = sorted(results, key=lambda x: x['total_mev_rewards_sol_last_epochs'], reverse=True)
        
        return sorted_results
    
    def generate_report(self, results: List[Dict[str, Any]], num_epochs: int = 5) -> None:
        """
        Generate and display the MEV rewards report.
        
        Args:
            results: List of validator analysis results
            num_epochs: Number of epochs analyzed
        """
        if not results:
            print("No results to display.")
            return
        
        print(f"\n--- MEV Rewards Report (Top {len(results)} Jito Validators) ---")
        print(f"Total MEV rewards shown for the last {num_epochs} epochs.")
        print("-" * 70)
        
        for res in results:
            commission_display = (
                f"{res['commission_bps'] / 100}%" 
                if res['commission_bps'] != 'N/A' 
                else 'N/A'
            )
            
            print(
                f"Rank {res['rank']}: {res['identity']} ({res['vote_account']})\n"
                f"  -> MEV Rewards: {res['total_mev_rewards_sol_last_epochs']:.6f} SOL\n"
                f"  -> MEV Commission: {commission_display}\n"
            )
        
        print("-" * 70)
    
    def save_report(self, results: List[Dict[str, Any]], filename: str = "jito_mev_report.json") -> bool:
        """
        Save the analysis report to a JSON file.
        
        Args:
            results: List of validator analysis results
            filename: Output filename
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            with open(filename, "w") as f:
                json.dump(results, f, indent=4)
            print(f"\nReport saved to {filename}")
            return True
        except IOError as e:
            print(f"Error saving report to {filename}: {e}")
            return False
    
    def get_validator_summary_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
            
        Returns:
            Dictionary containing summary statistics
        """
        if not results:
            return {}
        
        mev_rewards = [r['total_mev_rewards_sol_last_epochs'] for r in results]
        commissions = [r['commission_bps'] for r in results if r['commission_bps'] != 'N/A']
        
        stats = {
            "total_validators": len(results),
            "total_mev_rewards_sol": sum(mev_rewards),
            "average_mev_rewards_sol": sum(mev_rewards) / len(mev_rewards) if mev_rewards else 0,
            "max_mev_rewards_sol": max(mev_rewards) if mev_rewards else 0,
            "min_mev_rewards_sol": min(mev_rewards) if mev_rewards else 0,
            "average_commission_bps": sum(commissions) / len(commissions) if commissions else 0,
            "validators_with_zero_commission": len([c for c in commissions if c == 0]),
        }
        
        return stats
    
    def print_summary_stats(self, results: List[Dict[str, Any]]) -> None:
        """
        Print summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
        """
        stats = self.get_validator_summary_stats(results)
        
        if not stats:
            print("No statistics available.")
            return
        
        print("\n--- Summary Statistics ---")
        print(f"Total Validators Analyzed: {stats['total_validators']}")
        print(f"Total MEV Rewards: {stats['total_mev_rewards_sol']:.6f} SOL")
        print(f"Average MEV Rewards: {stats['average_mev_rewards_sol']:.6f} SOL")
        print(f"Max MEV Rewards: {stats['max_mev_rewards_sol']:.6f} SOL")
        print(f"Min MEV Rewards: {stats['min_mev_rewards_sol']:.6f} SOL")
        print(f"Average Commission: {stats['average_commission_bps']:.2f} bps")
        print(f"Validators with 0% Commission: {stats['validators_with_zero_commission']}")
        print("-" * 30)
