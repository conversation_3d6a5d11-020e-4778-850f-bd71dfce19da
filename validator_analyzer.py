import json
import requests
from typing import List, Dict, Any, Optional
from jito_client import <PERSON><PERSON><PERSON><PERSON>
from solana_client import SolanaClient
from utils import Utils


class ValidatorAnalyzer:
    """Main class for analyzing validator performance and MEV rewards."""

    VALIDATOR_NAMES_CACHE_FILE = "validator_names_mapping.json"

    def __init__(self, use_cache: bool = True, rate_limit_delay: float = 0.5):
        """
        Initialize the validator analyzer.

        Args:
            use_cache: Whether to use file caching for API responses
            rate_limit_delay: Delay between API requests in seconds
        """
        self.jito_client = JitoClient(rate_limit_delay=rate_limit_delay, use_cache=use_cache)
        self.solana_client = SolanaClient()
        self.use_cache = use_cache
        self.validator_names_cache = self._load_validator_names_cache()
        self._initialize_known_validators()
    
    def analyze_top_validators(self, limit: int = 100, num_epochs: int = 5) -> List[Dict[str, Any]]:
        """
        Analyze top validators by stake and fetch their MEV rewards.
        
        Args:
            limit: Number of top validators to analyze
            num_epochs: Number of recent epochs to analyze for MEV rewards
            
        Returns:
            List of validator analysis results
        """
        print(f"Fetching top {limit} Jito validators...")
        top_validators = self.jito_client.get_validators(limit=limit)
        
        if not top_validators:
            print("Failed to fetch validator data. Exiting.")
            return []
        
        results = []
        print("Processing validators and fetching MEV rewards...")
        
        for i, validator in enumerate(top_validators):
            vote_account = validator.get('vote_account')
            identity = validator.get('identity')
            
            if vote_account:
                # Try to get a meaningful validator name
                validator_name = self._get_validator_display_name(identity, vote_account)

                # Query MEV rewards with enhanced validator name for better cache naming
                mev_rewards_sol = self.jito_client.get_mev_rewards(
                    vote_account,
                    num_epochs=num_epochs,
                    validator_name=validator_name
                )

                # Try to get a meaningful validator name
                validator_name = self._get_validator_display_name(identity, vote_account)

                results.append({
                    "rank": i + 1,
                    "identity": identity,  # Keep original for compatibility
                    "validator_name": validator_name,  # Enhanced name field
                    "vote_account": vote_account,
                    "total_mev_rewards_sol_last_epochs": mev_rewards_sol,
                    "commission_bps": validator.get('mev_commission_bps', 'N/A'),
                    "active_stake": validator.get('active_stake', 0)
                })

                # Print progress with enhanced display name
                print(f"  [{i + 1}/{limit}] Fetched rewards for {validator_name} ({vote_account})")
        
        # Sort results by MEV rewards
        sorted_results = sorted(results, key=lambda x: x['total_mev_rewards_sol_last_epochs'], reverse=True)
        
        return sorted_results
    
    def generate_report(self, results: List[Dict[str, Any]], num_epochs: int = 5) -> None:
        """
        Generate and display the MEV rewards report.
        
        Args:
            results: List of validator analysis results
            num_epochs: Number of epochs analyzed
        """
        if not results:
            print("No results to display.")
            return
        
        print(f"\n--- MEV Rewards Report (Top {len(results)} Jito Validators) ---")
        print(f"Total MEV rewards shown for the last {num_epochs} epochs.")
        print("-" * 70)
        
        for res in results:
            commission_display = (
                f"{res['commission_bps'] / 100}%"
                if res['commission_bps'] != 'N/A'
                else 'N/A'
            )

            # Use the enhanced validator_name field for display
            display_name = res.get('validator_name', res.get('identity', 'Unknown'))

            print(
                f"Rank {res['rank']}: {display_name} ({res['vote_account']})\n"
                f"  -> MEV Rewards: {res['total_mev_rewards_sol_last_epochs']:.6f} SOL\n"
                f"  -> MEV Commission: {commission_display}\n"
            )
        
        print("-" * 70)
    
    def save_report(self, results: List[Dict[str, Any]],
                   filename: str = None,
                   limit: int = None,
                   num_epochs: int = None) -> bool:
        """
        Save the analysis report to a JSON file with descriptive naming.

        Args:
            results: List of validator analysis results
            filename: Custom filename (if None, generates descriptive name)
            limit: Number of validators analyzed (for filename generation)
            num_epochs: Number of epochs analyzed (for filename generation)

        Returns:
            True if saved successfully, False otherwise
        """
        from utils import Utils

        # Generate descriptive filename if not provided
        if filename is None:
            filename = Utils.generate_report_filename(
                base_name="jito_mev_report",
                limit=limit,
                num_epochs=num_epochs,
                extension="json"
            )

        try:
            with open(filename, "w") as f:
                json.dump(results, f, indent=4)
            print(f"\nReport saved to {filename}")
            return True
        except IOError as e:
            print(f"Error saving report to {filename}: {e}")
            return False
    
    def get_validator_summary_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
            
        Returns:
            Dictionary containing summary statistics
        """
        if not results:
            return {}
        
        mev_rewards = [r['total_mev_rewards_sol_last_epochs'] for r in results]
        commissions = [r['commission_bps'] for r in results if r['commission_bps'] != 'N/A']
        
        stats = {
            "total_validators": len(results),
            "total_mev_rewards_sol": sum(mev_rewards),
            "average_mev_rewards_sol": sum(mev_rewards) / len(mev_rewards) if mev_rewards else 0,
            "max_mev_rewards_sol": max(mev_rewards) if mev_rewards else 0,
            "min_mev_rewards_sol": min(mev_rewards) if mev_rewards else 0,
            "average_commission_bps": sum(commissions) / len(commissions) if commissions else 0,
            "validators_with_zero_commission": len([c for c in commissions if c == 0]),
        }
        
        return stats
    
    def print_summary_stats(self, results: List[Dict[str, Any]]) -> None:
        """
        Print summary statistics for the analyzed validators.
        
        Args:
            results: List of validator analysis results
        """
        stats = self.get_validator_summary_stats(results)
        
        if not stats:
            print("No statistics available.")
            return
        
        print("\n--- Summary Statistics ---")
        print(f"Total Validators Analyzed: {stats['total_validators']}")
        print(f"Total MEV Rewards: {stats['total_mev_rewards_sol']:.6f} SOL")
        print(f"Average MEV Rewards: {stats['average_mev_rewards_sol']:.6f} SOL")
        print(f"Max MEV Rewards: {stats['max_mev_rewards_sol']:.6f} SOL")
        print(f"Min MEV Rewards: {stats['min_mev_rewards_sol']:.6f} SOL")
        print(f"Average Commission: {stats['average_commission_bps']:.2f} bps")
        print(f"Validators with 0% Commission: {stats['validators_with_zero_commission']}")
        print("-" * 30)

    def print_validator_names_cache_stats(self) -> None:
        """Print statistics about the validator names cache."""
        if not self.validator_names_cache:
            print("No validator names cached.")
            return

        sources = {}
        for entry in self.validator_names_cache.values():
            if isinstance(entry, dict):
                source = entry.get("source", "unknown")
                sources[source] = sources.get(source, 0) + 1

        print(f"\n--- Validator Names Cache Statistics ---")
        print(f"Total cached validators: {len(self.validator_names_cache)}")
        for source, count in sources.items():
            print(f"  {source}: {count} validators")
        print("-" * 40)

    def _load_validator_names_cache(self) -> Dict[str, str]:
        """
        Load the validator names cache from file.

        Returns:
            Dictionary mapping vote accounts to validator names
        """
        if not self.use_cache:
            return {}

        cached_data = Utils.load_cached_data(self.VALIDATOR_NAMES_CACHE_FILE)
        if cached_data and isinstance(cached_data, dict):
            return cached_data

        return {}

    def _save_validator_names_cache(self) -> None:
        """Save the validator names cache to file."""
        if self.use_cache and self.validator_names_cache:
            Utils.save_cached_data(self.VALIDATOR_NAMES_CACHE_FILE, self.validator_names_cache)

    def _add_validator_name_to_cache(self, vote_account: str, name: str, source: str = "solana_rpc") -> None:
        """
        Add a validator name to the cache.

        Args:
            vote_account: The validator's vote account address
            name: The validator's name
            source: Source of the name (e.g., "solana_rpc", "known_mapping")
        """
        if name and name.strip():
            self.validator_names_cache[vote_account] = {
                "name": name.strip(),
                "source": source,
                "vote_account": vote_account
            }
            # Save immediately to persist the data
            self._save_validator_names_cache()

    def _initialize_known_validators(self) -> None:
        """Initialize the cache with known validators if not already present."""
        known_validators = self._get_known_validator_mapping()
        cache_updated = False

        for vote_account, name in known_validators.items():
            if vote_account not in self.validator_names_cache:
                self.validator_names_cache[vote_account] = {
                    "name": name,
                    "source": "known_mapping",
                    "vote_account": vote_account
                }
                cache_updated = True

        if cache_updated:
            self._save_validator_names_cache()

    def _get_validator_display_name(self, identity: str, vote_account: str) -> str:
        """
        Get a meaningful display name for a validator.

        Args:
            identity: The validator's identity from Jito API (may be null)
            vote_account: The validator's vote account address

        Returns:
            A meaningful display name for the validator
        """
        # If we have an identity from Jito, use it
        if identity and identity.lower() != 'none':
            return identity

        # Try to get validator name from various sources
        try:
            # 1. Check persistent cache first
            if vote_account in self.validator_names_cache:
                cached_entry = self.validator_names_cache[vote_account]
                if isinstance(cached_entry, dict):
                    name = cached_entry.get("name")
                    if name:
                        return name

            # 2. Check our known validator mapping
            known_validators = self._get_known_validator_mapping()
            if vote_account in known_validators:
                name = known_validators[vote_account]
                self._add_validator_name_to_cache(vote_account, name, "known_mapping")
                return name

            # 3. Try to fetch from Solana RPC
            validator_name = self._fetch_validator_name_from_solana_rpc(vote_account)
            if validator_name:
                self._add_validator_name_to_cache(vote_account, validator_name, "solana_rpc")
                return validator_name

            # 4. Fallback to shortened version of the vote account
            fallback_name = f"Validator-{vote_account[:8]}...{vote_account[-4:]}"
            return fallback_name

        except Exception as e:
            print(f"Error getting validator name for {vote_account}: {e}")
            # Final fallback to shortened vote account
            return f"Validator-{vote_account[:8]}...{vote_account[-4:]}"

    def _fetch_validator_name_from_solana_rpc(self, vote_account: str) -> Optional[str]:
        """
        Fetch validator name from Solana RPC.

        Args:
            vote_account: The validator's vote account address

        Returns:
            Validator name if found, None otherwise
        """
        try:
            # Try to get name from vote account data
            name = self.solana_client.get_validator_name_from_vote_account(vote_account)
            if name:
                return name

            # Try to get name from cluster nodes
            name = self.solana_client.get_validator_name_from_cluster_nodes(vote_account)
            if name:
                return name

            return None

        except Exception as e:
            print(f"Error fetching validator name from Solana RPC for {vote_account}: {e}")
            return None

    def _get_known_validator_mapping(self) -> dict:
        """
        Get a mapping of known validator vote accounts to their names.
        This includes well-known validators from the Solana ecosystem.

        Returns:
            Dictionary mapping vote accounts to validator names
        """
        # Known validators from the Solana ecosystem
        # This can be expanded or loaded from an external source
        return {
            "DdCNGDpP7qMgoAy6paFzhhak2EeyCZcgjH7ak5u5v28m": "Kiln",
            "CvSb7wdQAFpHuSpTYTJnX5SYH4hCfQ9VuGnqrKaKwycB": "Coinbase",
            "HZKopZYvv8v6un2H6KUNVQCnK5zM9emKKezvqhTBSpEc": "Binance Staking",
            "3N7s9zXMZ4QqvHQR15t5GNHyqc89KduzMP7423eWiD5g": "Figment",
            "CcaHc2L43ZWjwCHART3oZoJvHLAe9hzT2DJNUpBzoTN1": "Chorus One",
            "he1iusunGwqrNtafDtLdhsUQDFvo13z9sUa36PauBtk": "Helius",
            "26pV97Ce83ZQ6Kz9XT4td8tdoUFPTng8Fb8gPyc53dJx": "Solana Foundation",
            "CatzoSMUkTRidT5DwBxAC2pEtnwMBTpkCepHkFgZDiqb": "Catzo",
            "6D2jqw9hyVCpppZexquxa74Fn33rJzzBx38T58VucHx9": "Laine",
            "DumiCKHVqoCQKD8roLApzR5Fit8qGV5fVQsJV9sTZk4a": "Dumi",
            "9QU2QSxhb24FUX3Tu2FpczXjpK3VYrvRudywSZaM29mF": "Shinobi Systems",
            "KRAKEnMdmT4EfM8ykTFH6yLoCd5vNLcQvJwF66Y2dag": "Kraken",
            "BLADE1qNA1uNjRgER6DtUFf7FU3c1TWLLdpPeEcKatZ2": "Blade",
            "Chorus6Kis8tFHA7AowrPMcRJk3LbApHTYpgSNXzY5KE": "Chorus One",
            "CertusDeBmqN8ZawdkxK5kFGMwBXdudvWHYwtNgNhvLu": "Certus One",
            "DriFTm3wM9ugxhCA1K3wVQMSdC4Dv4LNmyZMmZiuHRpp": "Drift",
            "EARNynHRWg6GfyJCmrrizcZxARB3HVzcaasvNa8kBS72": "Earn",
            "LSTmLs1DENX82ihc7jU134mydiV3NDEPXsRrekAY6Ys": "Lido",
            # Add more known validators here as needed
        }
